<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PetHealthRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PetHealthRecordTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_pet_health_records_index()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/pet-health');

        $response->assertStatus(200);
        $response->assertViewIs('pet_health.index');
    }

    public function test_user_can_create_pet_health_record()
    {
        $user = User::factory()->create();

        $petData = [
            'pet_name' => 'Buddy',
            'species' => 'Dog',
            'breed' => 'Golden Retriever',
            'medical_history' => 'Vaccinated, healthy',
            'last_vaccination' => '2024-01-15',
        ];

        $response = $this->actingAs($user)->post('/pet-health', $petData);

        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('pet_health_records', [
            'user_id' => $user->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
            'breed' => 'Golden Retriever',
        ]);
    }

    public function test_user_can_only_see_their_own_records()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $record1 = PetHealthRecord::create([
            'user_id' => $user1->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
        ]);

        $record2 = PetHealthRecord::create([
            'user_id' => $user2->id,
            'pet_name' => 'Fluffy',
            'species' => 'Cat',
        ]);

        $response = $this->actingAs($user1)->get('/pet-health');

        $response->assertStatus(200);
        $response->assertSee('Buddy');
        $response->assertDontSee('Fluffy');
    }

    public function test_validation_errors_for_required_fields()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/pet-health', []);

        $response->assertSessionHasErrors(['pet_name', 'species']);
    }

    public function test_user_can_update_pet_health_record()
    {
        $user = User::factory()->create();

        $record = PetHealthRecord::create([
            'user_id' => $user->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
        ]);

        $updateData = [
            'pet_name' => 'Buddy Updated',
            'species' => 'Dog',
            'breed' => 'Labrador',
        ];

        $response = $this->actingAs($user)->put("/pet-health/{$record->id}", $updateData);

        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('pet_health_records', [
            'id' => $record->id,
            'pet_name' => 'Buddy Updated',
            'breed' => 'Labrador',
        ]);
    }

    public function test_user_can_delete_pet_health_record()
    {
        $user = User::factory()->create();

        $record = PetHealthRecord::create([
            'user_id' => $user->id,
            'pet_name' => 'Buddy',
            'species' => 'Dog',
        ]);

        $response = $this->actingAs($user)->delete("/pet-health/{$record->id}");

        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('pet_health_records', [
            'id' => $record->id,
        ]);
    }
}
