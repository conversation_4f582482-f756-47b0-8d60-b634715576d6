<?php

namespace App\Http\Controllers;

use App\Models\PetHealthRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PetHealthRecordController extends Controller
{
    public function index()
    {
        $records = PetHealthRecord::where('user_id', Auth::id())->get();
        return view('pet_health.index', compact('records'));
    }

    public function create()
    {
        return view('pet_health.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'pet_name' => 'required|string|max:255',
            'species' => 'required|string|max:255',
            'breed' => 'nullable|string|max:255',
            'medical_history' => 'nullable|string',
            'last_vaccination' => 'nullable|date',
        ]);

        try {
            PetHealthRecord::create([
                'user_id' => Auth::id(),
                'pet_name' => $request->pet_name,
                'species' => $request->species,
                'breed' => $request->breed,
                'medical_history' => $request->medical_history,
                'last_vaccination' => $request->last_vaccination,
            ]);

            return redirect()->route('pet_health.index')->with('success', 'Pet health record saved successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to save pet health record. Please try again.');
        }
    }

    public function show($id)
    {
        $record = PetHealthRecord::where('user_id', Auth::id())->findOrFail($id);
        return view('pet_health.show', compact('record'));
    }

    public function edit($id)
    {
        $record = PetHealthRecord::where('user_id', Auth::id())->findOrFail($id);
        return view('pet_health.edit', compact('record'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'pet_name' => 'required|string|max:255',
            'species' => 'required|string|max:255',
            'breed' => 'nullable|string|max:255',
            'medical_history' => 'nullable|string',
            'last_vaccination' => 'nullable|date',
        ]);

        try {
            $record = PetHealthRecord::where('user_id', Auth::id())->findOrFail($id);
            $record->update([
                'pet_name' => $request->pet_name,
                'species' => $request->species,
                'breed' => $request->breed,
                'medical_history' => $request->medical_history,
                'last_vaccination' => $request->last_vaccination,
            ]);

            return redirect()->route('pet_health.index')->with('success', 'Pet health record updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update pet health record. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $record = PetHealthRecord::where('user_id', Auth::id())->findOrFail($id);
            $record->delete();

            return redirect()->route('pet_health.index')->with('success', 'Pet health record deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete pet health record. Please try again.');
        }
    }
}
